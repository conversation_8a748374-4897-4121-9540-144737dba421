'ui';

ui.layout(
    <vertical id="mainLayout" bg="#FFFFFF">
        <button id="myButton" text="点击我" />
    </vertical>
);

ui.myButton.on("click", () => {
    // 完全隐藏主界面
    ui.setContentView(<vertical></vertical>); // 将主界面内容替换为空
    // 或者使用以下方法
    // activity.finish(); // 关闭当前活动

    // 显示浮动窗口
    let floatWindow = floaty.window(
        <vertical bg="#FFFFFF" padding="10">
            <button id="startButton" text="开始" />
            <button id="returnButton" text="返回" />
        </vertical>
    );

    // 设置浮动窗口位置
    floatWindow.setPosition(100, 100);

    // 开始按钮点击事件
    floatWindow.startButton.on("click", () => {
        toast("开始按钮被点击！");
    });

    // 返回按钮点击事件
    floatWindow.returnButton.on("click", () => {
        floatWindow.close(); // 关闭浮动窗口
        // 重新启动应用或重新设置主界面
        engines.execScriptFile("测试.js"); // 重新执行当前脚本
    });
});
