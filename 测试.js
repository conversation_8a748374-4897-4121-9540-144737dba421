'ui'; // 启用UI模式

// 创建主界面布局
ui.layout(
    <vertical id="mainLayout" bg="#FFFFFF" padding="20">
        <text text="多平台自动化工具" textSize="18sp" gravity="center" margin="0 0 20 0"/>
        <button id="myButton" text="启动" textSize="16sp" bg="#4CAF50" textColor="#FFFFFF" margin="10"/>
    </vertical>
);

// 全局变量存储浮动窗口
let floatWindow = null;

// 启动按钮点击事件
ui.myButton.on("click", () => {
    toast("正在启动浮动窗口..."); // 显示启动提示

    // 延迟执行，让用户看到提示
    setTimeout(() => {
        // 隐藏主界面 - 将Activity移到后台
        activity.moveTaskToBack(true);

        // 创建浮动窗口
        floatWindow = floaty.window(
            <vertical bg="#FFFFFF" padding="15" elevation="8dp" alpha="0.95">
                <text text="控制面板" textSize="14sp" gravity="center" textColor="#333333" margin="0 0 10 0"/>
                <horizontal>
                    <button id="startButton" text="开始执行" textSize="12sp" bg="#2196F3" textColor="#FFFFFF" layout_weight="1" margin="2"/>
                    <button id="pauseButton" text="暂停" textSize="12sp" bg="#FF9800" textColor="#FFFFFF" layout_weight="1" margin="2"/>
                </horizontal>
                <horizontal>
                    <button id="stopButton" text="停止" textSize="12sp" bg="#F44336" textColor="#FFFFFF" layout_weight="1" margin="2"/>
                    <button id="returnButton" text="返回" textSize="12sp" bg="#9E9E9E" textColor="#FFFFFF" layout_weight="1" margin="2"/>
                </horizontal>
            </vertical>
        );

        // 设置浮动窗口初始位置（屏幕右上角）
        floatWindow.setPosition(device.width - 200, 100);

        // 设置浮动窗口可拖拽
        floatWindow.setTouchable(true);

        toast("浮动窗口已启动"); // 启动成功提示

        // 开始执行按钮点击事件
        floatWindow.startButton.on("click", () => {
            toast("开始执行自动化任务！"); // 执行提示
            // 在这里添加您的自动化逻辑
            log("自动化任务开始执行");
        });

        // 暂停按钮点击事件
        floatWindow.pauseButton.on("click", () => {
            toast("任务已暂停"); // 暂停提示
            log("自动化任务已暂停");
        });

        // 停止按钮点击事件
        floatWindow.stopButton.on("click", () => {
            toast("任务已停止"); // 停止提示
            log("自动化任务已停止");
        });

        // 返回按钮点击事件
        floatWindow.returnButton.on("click", () => {
            toast("正在返回主界面..."); // 返回提示

            // 关闭浮动窗口
            if (floatWindow) {
                floatWindow.close();
                floatWindow = null;
            }

            // 将应用带回前台
            app.launchApp("Auto.js");

            // 延迟后重新显示主界面
            setTimeout(() => {
                // 重新执行脚本以显示主界面
                engines.execScriptFile("测试.js");
            }, 500);
        });

    }, 500); // 延迟500毫秒执行
});

// 脚本退出时清理浮动窗口
events.on("exit", () => {
    if (floatWindow) {
        floatWindow.close(); // 清理浮动窗口资源
        log("浮动窗口已清理");
    }
});
